"use client";

import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type Testimonial = {
  id: number;
  name: string;
  company: string;
  image: string;
  quote: string;
};

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    company: "Nairobi Retail",
    image: "/placeholder.svg?height=400&width=400",
    quote:
      "This platform has revolutionized how we verify Mobile-money transactions. It's fast and reliable!",
  },
  {
    id: 2,
    name: "<PERSON>",
    company: "Mombasa Traders",
    image: "/placeholder.svg?height=400&width=400",
    quote:
      "The API integration was seamless. We've automated our entire payment verification process.",
  },
  {
    id: 3,
    name: "<PERSON>",
    company: "Kisumu Enterprises",
    image: "/placeholder.svg?height=400&width=400",
    quote:
      "The reporting features have given us valuable insights into our transaction patterns.",
  },
];

export function TestimonialCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const nextSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) =>
      prev === testimonials.length - 1 ? 0 : prev + 1
    );
    setTimeout(() => setIsTransitioning(false), 500);
  };

  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) =>
      prev === 0 ? testimonials.length - 1 : prev - 1
    );
    setTimeout(() => setIsTransitioning(false), 500);
  };

  // Auto-advance slides
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentSlide]);

  return (
    <div className="relative w-full h-full overflow-hidden rounded-xl bg-gradient-to-b from-green-50 to-white dark:from-green-900 dark:to-gray-900">
      <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>

      {/* Slides */}
      <div className="relative h-full">
        {testimonials.map((testimonial, index) => (
          <div
            key={testimonial.id}
            className={cn(
              "absolute inset-0 flex flex-col items-center justify-center p-6 transition-opacity duration-500 ease-in-out",
              currentSlide === index ? "opacity-100 z-10" : "opacity-0 z-0"
            )}
          >
            <div className="w-24 h-24 mb-4 rounded-full overflow-hidden border-4 border-white dark:border-gray-800 shadow-lg">
              <img
                src={testimonial.image || "/placeholder.svg"}
                alt={testimonial.name}
                className="w-full h-full object-cover"
              />
            </div>

            <blockquote className="text-center mb-4 max-w-md">
              <p className="text-lg italic mb-2">"{testimonial.quote}"</p>
              <footer className="font-medium">
                <span className="block text-green-600 dark:text-green-400">
                  {testimonial.name}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {testimonial.company}
                </span>
              </footer>
            </blockquote>

            <div className="mt-4 flex items-center justify-center space-x-1">
              {testimonials.map((_, i) => (
                <span
                  key={i}
                  className={cn(
                    "block h-2 w-2 rounded-full transition-all duration-300",
                    currentSlide === i
                      ? "bg-green-600 w-4"
                      : "bg-gray-300 dark:bg-gray-600"
                  )}
                ></span>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Navigation buttons */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute left-2 top-1/2 -translate-y-1/2 z-20 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 rounded-full shadow-md"
        onClick={prevSlide}
        aria-label="Previous testimonial"
      >
        <ChevronLeft className="h-5 w-5" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        className="absolute right-2 top-1/2 -translate-y-1/2 z-20 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 rounded-full shadow-md"
        onClick={nextSlide}
        aria-label="Next testimonial"
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
    </div>
  );
}
