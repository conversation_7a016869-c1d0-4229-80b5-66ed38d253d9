import { apiGet, apiPost } from "@/lib/auth/api-client";
import { Api<PERSON>ey, DashboardAnalytics, PhoneNumberHash } from "@/types";

const sampleResponse = {
  id: 1,
  hash: "252bcd93b2fc4c2b2cb6d0f41b696a4f1fbfe6ef2b5e25a119b32f5f521a998e",
  phone_number: "254110010000",
  provider: "safaricom",
  new_balance: 104,
  created_at: "2025-05-29T13:42:00.207426Z",
  updated_at: "2025-05-29T13:42:00.207426Z",
};

export interface DecodedHash {
  id: string;
  hash: string;
  phone_number: string;
  new_balance?: number;
  provider: string;
  created_at: string;
  updated_at: string;
}

// Hash decoding service
export const hashingService = {
  // Decode a hash to get the phone number
  decodeHash: (hash: string, token: string | null): Promise<DecodedHash> => {
    return apiGet<DecodedHash>(`/phone_number_hashes/hash/${hash}`, token);
  },

  // Get hash by phone number
  getHashByPhoneNumber: (
    phoneNumber: string,
    token: string | null
  ): Promise<PhoneNumberHash> => {
    return apiGet<PhoneNumberHash>(
      `/phone_number_hashes/phone_number/${phoneNumber}`,
      token
    );
  },

  // Create a new phone number hash
  createPhoneNumberHash: (
    phoneNumber: string,
    token: string | null
  ): Promise<PhoneNumberHash> => {
    return apiGet<PhoneNumberHash>(
      `/phone_number_hashes/phone_number/${phoneNumber}`,
      token
    );
  },

  // Get all phone number hashes
  getAllPhoneNumberHashes: (
    token: string | null
  ): Promise<PhoneNumberHash[]> => {
    return apiGet<PhoneNumberHash[]>("/phone_number_hashes", token);
  },
};

// Analytics service
export const analyticsService = {
  getDashboardAnalytics: (token: string | null) => {
    return apiGet<DashboardAnalytics>("/analytics", token);
  },
};

// API Keys service
export const apiKeysService = {
  createApiKey: (
    data: { description: string; store_id: number },
    token: string | null
  ) => {
    return apiPost<ApiKey>("/api_keys", data, token);
  },

  getApiKeys: (token: string | null) => {
    return apiGet<{
      api_keys: ApiKey[];
      pagination: { count: number; page: number; per: number };
    }>("/api_keys", token);
  },

  getApiKeyById: (id: number, token: string | null) => {
    return apiGet(`/api_keys/${id}`, token);
  },
};

// Health check service
export const healthService = {
  checkHealth: () => {
    return apiGet<{ message: string; status: string }>("/health", null);
  },
};
