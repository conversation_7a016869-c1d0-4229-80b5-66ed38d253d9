"use client";

import type { ReactNode } from "react";
import ProtectedRoute from "@/lib/auth/protected-route";
import { useState, useEffect } from "react";
import { DashboardSidebar } from "@/components/dashboard/sidebar";
import { DashboardHeader } from "@/components/dashboard/header";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Sheet, SheetContent, SheetTitle } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { VisuallyHidden } from "@/components/ui/visually-hidden";

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  const isDesktop = useMediaQuery("(min-width: 1024px)");

  // Set initial sidebar state based on screen size
  useEffect(() => {
    setSidebarOpen(isDesktop);
  }, [isDesktop]);

  const toggleSidebar = () => {
    if (isDesktop) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setMobileOpen(!mobileOpen);
    }
  };

  const handleMobileNavigation = () => {
    if (!isDesktop) {
      setMobileOpen(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen">
        {/* Desktop Sidebar */}
        {isDesktop && <DashboardSidebar isOpen={sidebarOpen} />}

        {/* Mobile Sidebar as Dialog */}
        {!isDesktop && (
          <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
            <SheetContent side="left" className="p-0 w-[260px]">
              <VisuallyHidden>
                <SheetTitle>Dashboard Navigation</SheetTitle>
              </VisuallyHidden>
              <DashboardSidebar
                isOpen={true}
                onNavigate={handleMobileNavigation}
              />
            </SheetContent>
          </Sheet>
        )}

        <div
          className={cn(
            "flex flex-col flex-1 transition-all duration-300 ease-in-out overflow-hidden",
            isDesktop && sidebarOpen ? "lg:ml-[260px]" : ""
          )}
        >
          <DashboardHeader
            toggleSidebar={toggleSidebar}
            sidebarOpen={isDesktop ? sidebarOpen : mobileOpen}
            isMobile={!isDesktop}
          />
          <main className="flex-1 p-4 md:p-6 overflow-auto">{children}</main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
