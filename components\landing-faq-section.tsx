import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function LandingFaqSection() {
  // Selected key FAQs for the landing page
  const keyFaqs = [
    {
      question: "What is the Compliance And Risk Management System?",
      answer:
        "The Risk Management and Compliance System is a secure, cloud-based platform—accessible via web and mobile—that helps individuals and organizations in Kenya manage their legal, tax, and internal compliance obligations. It provides real-time tracking, automated alerts, centralized recordkeeping, and insightful reporting to ensure you stay ahead of regulatory requirements, avoid penalties, and maintain operational integrity. Whether you're a freelancer, consultant, SME, or corporate entity, the system simplifies complex compliance tasks and supports good governance practices—all in one easy-to-use dashboard.",
    },
    {
      question: "How much does the service cost?",
      answer: `
       We offer flexible pricing plans tailored to different needs:

    Starter – KES 1,500/month or KES 15,000/year
    Ideal for individuals and freelancers. Includes personal compliance tracking, reminders, email alerts, and document storage up to 2GB.

    Business – KES 5,000/month or KES 50,000/year
    Designed for SMEs and small teams. Offers up to 10 users, internal policy tools, role-based access, SMS alerts, and 10GB storage.

    Enterprise – Custom pricing
    Best for large organizations with advanced needs. Includes unlimited users, API access, real-time dashboards, dedicated support, and custom modules.

We also offer discounts on annual subscriptions and can provide custom quotes for specific compliance requirements. Contact us to find the best fit for your organization.
       `,
    },
    {
      question: "Which countries are supported?",
      answer:
        "We are currently supporting services in Kenya, Tanzania, Democratic Republic of Congo, Mozambique, Lesotho, Ghana, and Egypt. We're continuously expanding our coverage to include more regions.",
    },
    {
      question: "How do I integrate with your API?",
      answer:
        "Integrating with our API is straightforward. Sign up for an account, generate an API key, and use our RESTful API to communicate with the Core system. We provide comprehensive documentation, code examples, and SDKs to make integration as simple as possible.",
    },
    {
      question: "Is your service secure and compliant?",
      answer:
        "Yes, absolutely. Security and compliance are at the core of our platform. We use industry-standard encryption protocols, secure cloud infrastructure, and regular security audits to protect your data. All information is stored and transmitted securely in accordance with data protection laws and best practices. In addition, our system is designed to align with local regulatory frameworks and corporate governance standards, ensuring that both your data and your compliance processes remain secure, reliable, and audit-ready.",
    },
  ];

  return (
    <section
      id="faq"
      className="w-full py-12 md:py-24 lg:py-32 bg-gray-50 dark:bg-gray-900"
    >
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Frequently Asked Questions
            </h2>
            <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
              Find answers to common questions about our risk and compliance
              management service.
            </p>
          </div>
        </div>

        <div className="mx-auto max-w-3xl mt-12">
          <Accordion type="single" collapsible className="space-y-4">
            {keyFaqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`faq-${index}`}
                className="border rounded-lg px-4"
              >
                <AccordionTrigger className="text-left hover:no-underline py-4">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="pt-2 pb-4 text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="mt-8 text-center">
            <Link href="/faq">
              <Button variant="outline" size="lg">
                View All FAQs
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
