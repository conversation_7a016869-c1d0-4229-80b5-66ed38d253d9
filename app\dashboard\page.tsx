"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CreditCard, Hash } from "lucide-react";
import Link from "next/link";
import { DashboardStats } from "@/components/dashboard/stats";
import { HashDecodeForm } from "@/components/dashboard/hash-decode-form";
import { SingleHashDecoder } from "@/components/dashboard/single-hash-decoder";
import { SingleHashGenerator } from "@/components/dashboard/single-hash-generator";
import { TransactionDetailsModal } from "@/components/dashboard/transaction-details-modal";
import { PhoneNumberHash } from "@/types";
import { analyticsService, healthService } from "@/services/api";
import { useAuth } from "@/context/auth-context";

export default function DashboardPage() {
  const { token } = useAuth();
  const [selectedTransaction, setSelectedTransaction] =
    useState<PhoneNumberHash | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [healthStatus, setHealthStatus] = useState<{
    message: string;
    status: string;
    loading: boolean;
    time: Date;
  }>({
    message: "Operational",
    status: "Ok",
    loading: false,
    time: new Date(),
  });

  const [accountBalance, setAccountBalance] = useState({
    amount: 750.0,
    loading: false,
  });

  const [accountNumber, setAccountNumber] = useState("");

  useEffect(() => {
    fetchAccountBalance();
  }, [token]);

  const fetchAccountBalance = () => {
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.user_credits) {
          setAccountBalance({
            amount: data.user_credits,
            loading: false,
          });

          setAccountNumber(data.account_number);
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAccountBalance((prev) => ({ ...prev, loading: false }));
      });
  };

  const handleDecodedTransaction = (transaction: PhoneNumberHash) => {
    setSelectedTransaction(transaction);
    setIsModalOpen(true);
  };

  const handleHealthCheck = () => {
    setHealthStatus((prev) => ({ ...prev, loading: true }));
    healthService
      .checkHealth()
      .then((data) => {
        setHealthStatus((prev) => ({
          ...prev,
          message: data.message,
          status: data.status,
          loading: false,
          time: new Date(),
        }));
      })
      .catch((error) => {
        toast.error("Failed to perform check!");
        setHealthStatus((prev) => ({ ...prev, loading: false }));
      });
  };

  return (
    <div className="flex flex-col gap-6">
      <p className="text-muted-foreground">
        Welcome back! Here's an overview of your account
      </p>

      <DashboardStats />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="hidden">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Hash Phone Number
              </CardTitle>
              <Hash className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <HashDecodeForm />
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Current Balance
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {accountBalance.loading
                  ? "Loading..."
                  : `${accountBalance.amount.toFixed(2)} Credits`}
              </div>
              {/* Last top-up: 100 credits on May 15, 2023 */}
              <p className="text-xs text-muted-foreground mt-1">
                Enough for {Math.floor(accountBalance.amount * 2)} requests
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
              >
                <Link href="/dashboard/topup">Top Up Credits</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Status</CardTitle>
              <div
                className={cn(
                  "h-3 w-3 rounded-full",
                  healthStatus.status === "Ok" ? "bg-green-500" : "bg-red-500"
                )}
              />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{healthStatus.message}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Last check: {format(healthStatus.time, "dd MMM yyyy")} at{" "}
                {format(healthStatus.time, "hh:mm a")}
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
                onClick={handleHealthCheck}
                disabled={healthStatus.loading}
              >
                {healthStatus.loading ? "Checking..." : "Check Again"}
              </Button>
            </CardContent>
          </Card>
        </div>
        <div className="col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Account Number
              </CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{accountNumber}</div>
              {/* Last top-up: 100 credits on May 15, 2023 */}
              <p className="text-xs text-muted-foreground mt-1">
                For credit top ups. Paybill 4044085.
              </p>
              <Button
                size="sm"
                className="mt-4 bg-green-600 hover:bg-green-700"
              >
                <Link href="/dashboard/topup">Top Up Credits</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* <Tabs defaultValue="decode">
        <TabsList>
          <TabsTrigger value="decode">Decode Hash</TabsTrigger>
          <TabsTrigger value="generate">Generate Hash</TabsTrigger>
        </TabsList>
        <TabsContent value="decode" className="space-y-4"> 
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashDecoder onDecodeSuccess={handleDecodedTransaction} />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="generate" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <SingleHashGenerator
                onGenerateSuccess={handleDecodedTransaction}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs> */}

      {/* Transaction Details Modal */}
      <TransactionDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        transaction={selectedTransaction}
      />
    </div>
  );
}
