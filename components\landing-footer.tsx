import Link from "next/link";

export function LandingFooter() {
  return (
    <footer className="w-full border-t py-6 md:py-8">
      <div className="container flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-col gap-2">
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-6 w-6 rounded-full bg-green-600 flex items-center justify-center">
              <span className="font-bold text-white text-xs">C</span>
            </div>
            <span className="font-bold">Compliance 360</span>
          </Link>
          <p className="text-sm text-muted-foreground">
            &copy; {new Date().getFullYear()} Compliance 360. All rights
            reserved.
          </p>
        </div>
        <div className="flex flex-col md:flex-row gap-4 md:gap-6">
          <Link
            href="/terms"
            className="text-sm text-muted-foreground hover:underline underline-offset-4"
          >
            Terms of Service
          </Link>
          <Link
            href="/privacy"
            className="text-sm text-muted-foreground hover:underline underline-offset-4"
          >
            Privacy Policy
          </Link>
          <Link
            href="/cookie-policy"
            className="text-sm text-muted-foreground hover:underline underline-offset-4"
          >
            Cookie Policy
          </Link>
          <Link
            href="/faq"
            className="text-sm text-muted-foreground hover:underline underline-offset-4"
          >
            FAQ
          </Link>
          <Link
            href="/contact"
            className="text-sm text-muted-foreground hover:underline underline-offset-4"
          >
            Contact Us
          </Link>
        </div>
      </div>
    </footer>
  );
}
