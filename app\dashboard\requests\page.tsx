"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, Filter, Upload } from "lucide-react";
import { SingleHashDecoder } from "@/components/dashboard/single-hash-decoder";
import {
  TransactionDetailsModal,
  type TransactionDetails,
} from "@/components/dashboard/transaction-details-modal";
import { DecodedHash } from "@/services/api";

const mockTransaction = {
  id: "TRX-" + Math.floor(Math.random() * 10000),
  hash: "HASH-" + Math.floor(Math.random() * 10000),
  date: new Date().toISOString().split("T")[0],
  time: new Date().toTimeString().split(" ")[0],
  amount: "KSh " + (Math.floor(Math.random() * 10000) + 100),
  phoneNumber: "+254 7" + Math.floor(Math.random() * ********),
  telcoProvider: "Safaricom",
  transactionType: "Customer Payment",
  status: "completed",
  recipientName: "John Doe Merchant",
  recipientAccount: "Business Account",
  reference: "INV-" + Math.floor(Math.random() * 1000),
  description: "Payment for services",
};

export default function RequestsPage() {
  const [selectedTransaction, setSelectedTransaction] =
    useState<TransactionDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Mock data for requests
  const requests = [
    {
      id: "REQ-001",
      hash: "200020001234567890123456789012345678901234",
      date: "2023-05-20",
      status: "completed",
      source: "API",
      result: "Success",
    },
    {
      id: "REQ-002",
      hash: "200020002345678901234567890123456789012345",
      date: "2023-05-19",
      status: "completed",
      source: "Dashboard",
      result: "Success",
    },
    {
      id: "REQ-003",
      hash: "200020003456789012345678901234567890123456",
      date: "2023-05-18",
      status: "failed",
      source: "API",
      result: "Invalid hash format",
    },
    {
      id: "REQ-004",
      hash: "************890123456789012345678901234567",
      date: "2023-05-17",
      status: "completed",
      source: "File Upload",
      result: "Success",
    },
    {
      id: "REQ-005",
      hash: "200020005678901234567890123456789012345678",
      date: "2023-05-16",
      status: "processing",
      source: "Dashboard",
      result: "Pending",
    },
    {
      id: "REQ-006",
      hash: "200020006789012345678901234567890123456789",
      date: "2023-05-15",
      status: "completed",
      source: "API",
      result: "Success",
    },
    {
      id: "REQ-007",
      hash: "200020007890123456789012345678901234567890",
      date: "2023-05-14",
      status: "completed",
      source: "Dashboard",
      result: "Success",
    },
    {
      id: "REQ-008",
      hash: "200020008901234567890123456789012345678901",
      date: "2023-05-13",
      status: "failed",
      source: "API",
      result: "Hash not found",
    },
  ];

  const handleViewDetails = (request: any) => {
    // Create a mock transaction details object based on the request
    const transactionDetails: TransactionDetails = {
      id: request.id,
      hash: request.hash,
      created_at: request.date,
      updated_at:
        "14:" +
        Math.floor(Math.random() * 60)
          .toString()
          .padStart(2, "0") +
        ":" +
        Math.floor(Math.random() * 60)
          .toString()
          .padStart(2, "0"),
      amount: "KSh " + (Math.floor(Math.random() * 10000) + 100),
      phone_number: "+254 7" + Math.floor(Math.random() * ********),
      provider: "Safaricom",
      transactionType: "Customer Payment",
      status: request.status,
      recipientName: "John Doe Merchant",
      recipientAccount: "Business Account",
      reference: "INV-" + Math.floor(Math.random() * 1000),
      description: "Payment for services",
    };

    setSelectedTransaction(transactionDetails);
    setIsModalOpen(true);
  };

  const handleDecodedTransaction = (transaction: DecodedHash) => {
    setSelectedTransaction({ ...mockTransaction, ...transaction });
    setIsModalOpen(true);
  };

  return (
    <div className="flex flex-col gap-6">
      <p className="text-muted-foreground">
        View and manage your Phone hash decoding requests.
      </p>

      {/* Single Hash Decoder */}
      <SingleHashDecoder onDecodeSuccess={handleDecodedTransaction} />

      <Tabs defaultValue="all">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <TabsList>
            <TabsTrigger value="all">All Requests</TabsTrigger>
            <TabsTrigger value="api">API Requests</TabsTrigger>
            <TabsTrigger value="upload">File Uploads</TabsTrigger>
          </TabsList>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button size="sm" className="bg-green-600 hover:bg-green-700">
              <Upload className="mr-2 h-4 w-4" />
              Upload File
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="space-y-4">
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Upload Transaction Hashes</CardTitle>
              <CardDescription>
                Upload a CSV file containing Mobile-money transaction hashes for
                bulk processing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 items-end">
                <div className="space-y-2 flex-1">
                  <Label htmlFor="file-upload">Upload CSV File</Label>
                  <Input id="file-upload" type="file" />
                  <p className="text-xs text-muted-foreground">
                    The CSV file should have one hash per line.
                  </p>
                </div>
                <Button className="bg-green-600 hover:bg-green-700 whitespace-nowrap">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload and Process
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request ID</TableHead>
                  <TableHead>Hash</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell className="font-medium">{request.id}</TableCell>
                    <TableCell className="font-mono text-xs">
                      {request.hash.substring(0, 15)}...
                    </TableCell>
                    <TableCell>{request.date}</TableCell>
                    <TableCell>{request.source}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          request.status === "completed"
                            ? "success"
                            : request.status === "processing"
                            ? "outline"
                            : "destructive"
                        }
                      >
                        {request.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{request.result}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewDetails(request)}
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View details</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request ID</TableHead>
                  <TableHead>Hash</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requests
                  .filter((request) => request.source === "API")
                  .map((request) => (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium">
                        {request.id}
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {request.hash.substring(0, 15)}...
                      </TableCell>
                      <TableCell>{request.date}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            request.status === "completed"
                              ? "success"
                              : request.status === "processing"
                              ? "outline"
                              : "destructive"
                          }
                        >
                          {request.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{request.result}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewDetails(request)}
                        >
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View details</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Upload Transaction Hashes</CardTitle>
              <CardDescription>
                Upload a CSV file containing Mobile-money transaction hashes for
                bulk processing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 items-end">
                <div className="space-y-2 flex-1">
                  <Label htmlFor="file-upload-tab">Upload CSV File</Label>
                  <Input id="file-upload-tab" type="file" />
                  <p className="text-xs text-muted-foreground">
                    The CSV file should have one hash per line.
                  </p>
                </div>
                <Button className="bg-green-600 hover:bg-green-700 whitespace-nowrap">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload and Process
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request ID</TableHead>
                  <TableHead>File Name</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Hashes</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">FILE-001</TableCell>
                  <TableCell>transactions-may-20.csv</TableCell>
                  <TableCell>2023-05-20</TableCell>
                  <TableCell>
                    <Badge variant="success">completed</Badge>
                  </TableCell>
                  <TableCell>25</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View details</span>
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">FILE-002</TableCell>
                  <TableCell>transactions-may-15.csv</TableCell>
                  <TableCell>2023-05-15</TableCell>
                  <TableCell>
                    <Badge variant="success">completed</Badge>
                  </TableCell>
                  <TableCell>18</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View details</span>
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      {/* Transaction Details Modal */}
      <TransactionDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        transaction={selectedTransaction}
      />
    </div>
  );
}
