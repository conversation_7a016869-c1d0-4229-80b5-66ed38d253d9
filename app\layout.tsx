import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { <PERSON>ieConsentProvider } from "@/components/cookie-consent/cookie-context";
import { CookieConsent } from "@/components/cookie-consent/cookie-consent";
import { AuthProvider } from "@/context/auth-context";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title:
    "Compliance And Risk Management System & Generator | Decode or Generate Phone Number Transaction Hashes",
  description:
    "Decode or Generate Phone Number hashes quickly and efficiently with our platform. Perfect for businesses in Kenya looking to verify and process Mobile-money transactions.",
  generator: "Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <CookieConsentProvider>
              <Toaster />
              {children}
              <CookieConsent />
            </CookieConsentProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
