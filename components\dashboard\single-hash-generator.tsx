"use client";

import type React from "react";

import { useState } from "react";
import { useAuth } from "@/context/auth-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
// import type { TransactionDetails } from "./transaction-details-modal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { hashingService } from "@/services/api";
import { PhoneNumberHash } from "@/types";

interface SingleHashGeneratorProps {
  onGenerateSuccess?: (transaction: PhoneNumberHash) => void;
}

export function SingleHashGenerator({
  onGenerateSuccess,
}: SingleHashGeneratorProps) {
  const { token } = useAuth();
  const [mobileNumber, setMobileNumber] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    data?: PhoneNumberHash;
  } | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!mobileNumber.trim()) return;

    setIsLoading(true);
    setResult(null);
    hashingService
      .createPhoneNumberHash(mobileNumber, token)
      .then((response) => {
        // populate the result with the response data
        setResult(() => ({
          success: true,
          message: "Hash generated successfully.",
          data: response,
        }));
        // invoke the modal
        if (onGenerateSuccess && response) {
          onGenerateSuccess(response);
        }
      })
      .catch((error) => {
        setResult(() => ({
          success: false,
          message: error?.response?.data?.message || "Failed to generate hash.",
        }));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Hash Phone Number</CardTitle>
        <CardDescription>
          Convert a phone number into a secure hash value.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Input
              placeholder="Enter a valid Kenyan phone number e.g. 254712345678"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(e.target.value)}
              className="flex-1 font-mono"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={isLoading || !mobileNumber.trim()}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                "Generate Hash"
              )}
            </Button>
          </div>

          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
