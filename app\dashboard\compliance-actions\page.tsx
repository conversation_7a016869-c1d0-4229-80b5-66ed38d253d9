"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowDown, ArrowUp, CreditCard, Wallet } from "lucide-react";
import Link from "next/link";
import { analyticsService } from "@/services/api";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";

export default function BalancePage() {
  const { token } = useAuth();
  const [accountBalance, setAccountBalance] = useState({
    amount: 245,
    loading: false,
  });
  const [creditsUsed, setCreditsUsed] = useState(55);
  const [creditsAdded, setCreditsAdded] = useState(300);

  useEffect(() => {
    fetchAccountBalance();
  }, [token]);

  const fetchAccountBalance = () => {
    setAccountBalance((prev) => ({ ...prev, loading: true }));
    analyticsService
      .getDashboardAnalytics(token)
      .then((data) => {
        if (data && data.user_credits) {
          setAccountBalance({
            amount: data.user_credits,
            loading: false,
          });
        }
      })
      .catch((error) => {
        toast.error("Failed to fetch account balance");
        setAccountBalance((prev) => ({ ...prev, loading: false }));
      });
  };

  // Mock data for transaction history
  const transactions = [
    {
      id: "TRX-001",
      date: "2023-05-20",
      type: "Top-up",
      amount: "+100 Credits",
      method: "M-Pesa",
      status: "completed",
    },
    {
      id: "TRX-002",
      date: "2023-05-19",
      type: "Usage",
      amount: "-5 Credits",
      method: "API Request",
      status: "completed",
    },
    {
      id: "TRX-003",
      date: "2023-05-18",
      type: "Usage",
      amount: "-10 Credits",
      method: "File Upload",
      status: "completed",
    },
    {
      id: "TRX-004",
      date: "2023-05-15",
      type: "Top-up",
      amount: "+200 Credits",
      method: "Credit Card",
      status: "completed",
    },
    {
      id: "TRX-005",
      date: "2023-05-10",
      type: "Usage",
      amount: "-15 Credits",
      method: "API Request",
      status: "completed",
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      <p className="text-muted-foreground">
        View your current balance and transaction history.
      </p>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Balance
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {accountBalance.loading
                ? "Loading..."
                : `${accountBalance.amount.toFixed(2)} Credits`}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Last updated: {new Date().toLocaleDateString()}
            </p>
            <Button className="mt-4 bg-green-600 hover:bg-green-700">
              <Link href="/dashboard/topup">Top Up Credits</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Credits Used (This Month)
            </CardTitle>
            <ArrowDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{creditsUsed} Credits</div>
            <p className="text-xs text-muted-foreground mt-1">
              +15% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Credits Added (This Month)
            </CardTitle>
            <ArrowUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">300 Credits</div>
            <p className="text-xs text-muted-foreground mt-1">
              +50% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="all">All Transactions</TabsTrigger>
            <TabsTrigger value="topups">Top-ups</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
          </TabsList>
          <Button variant="outline" size="sm">
            <Wallet className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
        <TabsContent value="all" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">
                      {transaction.id}
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell>{transaction.type}</TableCell>
                    <TableCell
                      className={
                        transaction.type === "Top-up"
                          ? "text-green-600"
                          : "text-red-600"
                      }
                    >
                      {transaction.amount}
                    </TableCell>
                    <TableCell>{transaction.method}</TableCell>
                    <TableCell className="capitalize">
                      {transaction.status}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="topups" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions
                  .filter((t) => t.type === "Top-up")
                  .map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">
                        {transaction.id}
                      </TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.type}</TableCell>
                      <TableCell className="text-green-600">
                        {transaction.amount}
                      </TableCell>
                      <TableCell>{transaction.method}</TableCell>
                      <TableCell className="capitalize">
                        {transaction.status}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="usage" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions
                  .filter((t) => t.type === "Usage")
                  .map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">
                        {transaction.id}
                      </TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.type}</TableCell>
                      <TableCell className="text-red-600">
                        {transaction.amount}
                      </TableCell>
                      <TableCell>{transaction.method}</TableCell>
                      <TableCell className="capitalize">
                        {transaction.status}
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
