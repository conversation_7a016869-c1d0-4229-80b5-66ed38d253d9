"use client";

import { useAuth } from "@/context/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ModeToggle } from "@/components/mode-toggle";
import { Menu, PanelLeft, User, LogOut } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { sidebarItems } from "./sidebar";

function PageTitle() {
  const pathname = usePathname();

  // Find the matching sidebar item for the current path
  const getCurrentPageTitle = () => {
    // Exact match
    const exactMatch = sidebarItems.find((item) => item.href === pathname);
    if (exactMatch) return exactMatch.title;

    // Check if it's a subpath of any sidebar item
    for (const item of sidebarItems) {
      if (pathname?.startsWith(item.href) && item.href !== "/dashboard") {
        return item.title;
      }
    }

    // Default to Dashboard
    return "Dashboard";
  };

  return <h1 className="text-xl font-semibold">{getCurrentPageTitle()}</h1>;
}

export function DashboardHeader({
  toggleSidebar,
  sidebarOpen,
  isMobile,
}: {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
  isMobile: boolean;
}) {
  const { logout, user } = useAuth();
  const tooltipText = sidebarOpen ? "Hide sidebar" : "Show sidebar";

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="px-4 md:px-6 flex h-16 items-center">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                onClick={toggleSidebar}
                className="mr-4"
                aria-label={tooltipText}
              >
                {sidebarOpen && !isMobile ? (
                  <PanelLeft className="h-5 w-5" />
                ) : (
                  <Menu className="h-5 w-5" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <PageTitle />
        <div className="flex-1" />
        <div className="flex items-center gap-2">
          <ModeToggle />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <User className="h-5 w-5" />
                <span className="sr-only">User menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                {user ? `${user.first_name} ${user.last_name}` : `My Account`}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/dashboard/settings">Settings</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/topup">Billing</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <span onClick={logout} className="gap-2">
                  <LogOut className="h-4 w-4" />
                  Logout
                </span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
