import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LandingHeader } from "@/components/landing-header";
import { LandingFooter } from "@/components/landing-footer";
import { LandingFaqSection } from "@/components/landing-faq-section";
import {
  CheckCircle,
  Code,
  Shield,
  Zap,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import Image from "next/image";
import { ContactForm } from "@/components/contact-form";

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-green-50 to-white dark:from-green-950 dark:to-background overflow-hidden relative">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-green-100/50 to-transparent dark:from-green-900/30 -z-10"></div>
          <div className="absolute bottom-0 left-0 w-1/2 h-1/3 bg-gradient-to-t from-green-100/30 to-transparent dark:from-green-900/20 -z-10"></div>

          <div className="container px-4 md:px-6 relative">
            <div className="grid gap-6 lg:grid-cols-[0.9fr_1.1fr] lg:gap-12">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    Simplify Risk & Compliance Management
                  </h1>
                  {/* Add a bit of spacing */}
                  <div className="h-4"></div>
                  <p className="max-w-[600px] text-muted-foreground md:text-x2">
                    Stay compliant with local regulations and proactively manage
                    your company's risk. Our Comprehensive platform helps you
                    navigate the complex compliance requirements with ease and
                    confidence.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Link href="/dashboard">
                    <Button
                      size="lg"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Get Started
                    </Button>
                  </Link>
                  <Link href="#pricing">
                    <Button size="lg" variant="outline">
                      View Pricing
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="mx-auto w-full h-[400px] md:h-[450px] overflow-hidden rounded-xl shadow-lg relative border-4 border-white/80 dark:border-gray-800/50 backdrop-blur-sm">
                {/* Decorative corner accents */}
                <div className="absolute top-0 left-0 w-16 h-16 border-t-4 border-l-4 border-green-500/70 rounded-tl-lg -m-1"></div>
                <div className="absolute top-0 right-0 w-16 h-16 border-t-4 border-r-4 border-green-500/70 rounded-tr-lg -m-1"></div>
                <div className="absolute bottom-0 left-0 w-16 h-16 border-b-4 border-l-4 border-green-500/70 rounded-bl-lg -m-1"></div>
                <div className="absolute bottom-0 right-0 w-16 h-16 border-b-4 border-r-4 border-green-500/70 rounded-br-lg -m-1"></div>

                <Image
                  src="/images/ninthgrid-d6-bg-lCvZY-unsplash.jpg"
                  alt="Happy user working with Compliance And Risk Management System"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 60vw, 50vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-green-900/70 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6">
                  <p className="text-white text-base md:text-lg font-medium drop-shadow-md">
                    Risk & Compliance Made Easy
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Why Choose Our Compliance And Risk Management System?
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  We provide a secure, all-in-one platform that empowers
                  individuals and organizations to effortlessly manage their
                  legal, tax, and internal compliance obligations.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 lg:grid-cols-3">
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <Zap className="h-12 w-12 text-green-600" />
                <h3 className="text-xl font-bold">Realtime Tracking</h3>
                <p className="text-center text-muted-foreground">
                  Monitor all your compliance activities as they happen, in real
                  time.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <Shield className="h-12 w-12 text-green-600" />
                <h3 className="text-xl font-bold">Automated Alerts</h3>
                <p className="text-center text-muted-foreground">
                  Never miss a deadline with smart, timely compliance
                  notifications.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <Code className="h-12 w-12 text-green-600" />
                <h3 className="text-xl font-bold">Centralized Recordkeeping</h3>
                <p className="text-center text-muted-foreground">
                  Access all your compliance records from one secure, central
                  location.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-50 dark:bg-gray-900">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  How It Works
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  A simple 4-step process to help you stay compliant, organized,
                  and in control—every step of the way.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100">
                  1
                </div>
                <h3 className="text-xl font-bold">Sign Up</h3>
                <p className="text-center text-muted-foreground">
                  Create your account and customize your compliance
                  profile—whether you’re an individual or a business.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100">
                  2
                </div>
                <h3 className="text-xl font-bold">Add Obligations</h3>
                <p className="text-center text-muted-foreground">
                  Input or sync your tax, legal, and policy requirements. Set
                  due dates, rules, and reminders.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100">
                  3
                </div>
                <h3 className="text-xl font-bold">Track & Get Alerts</h3>
                <p className="text-center text-muted-foreground">
                  Monitor your compliance status in real time and receive
                  automated alerts before deadlines or risks arise.
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 border rounded-lg p-6 shadow-sm">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100 text-green-900 dark:bg-green-900 dark:text-green-100">
                  4
                </div>
                <h3 className="text-xl font-bold">Access Reports & Records</h3>
                <p className="text-center text-muted-foreground">
                  Download reports, view your history, and keep all documents
                  safely stored in one centralized place.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Simple, Transparent Pricing
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  Choose the plan that works best for your business needs.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-3">
              <div className="flex flex-col border rounded-lg p-6 shadow-sm">
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold">Starter</h3>
                  <p className="text-muted-foreground">
                    Perfect for small businesses just getting started.
                  </p>
                </div>
                <div className="mt-4 flex flex-wrap items-baseline text-3xl font-bold">
                  KSh 1,000
                  <span className="ml-1 text-base font-medium text-muted-foreground">
                    / month
                  </span>
                </div>
                <ul className="mt-6 space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Personal compliance tracking</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Tax & statutory reminders</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Automated alerts (Email only)</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Access via web & mobile</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Monthly activity reports</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Centralized document storage (Up to 2GB)</span>
                  </li>
                </ul>
                <div className="mt-6">
                  <Link href="/dashboard/topup">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="flex flex-col border rounded-lg p-6 shadow-sm bg-green-50 dark:bg-green-900/20">
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold">Business</h3>
                  <p className="text-muted-foreground">
                    Ideal for growing businesses with moderate volume.
                  </p>
                </div>
                <div className="mt-4 flex flex-wrap items-baseline text-3xl font-bold">
                  KSh 5000
                  <span className="ml-1 text-base font-medium text-muted-foreground">
                    / month
                  </span>
                </div>
                <ul className="mt-6 space-y-3">
                  {/* <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Up to 10 users</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Role-based access control</span>
                  </li> */}
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Business compliance tracking</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Tax & statutory reminders</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Automated alerts (Email + SMS) </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Access via web & mobile</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Monthly activity reports</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Centralized document storage (Up to 10GB)</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Internal policy compliance tools</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Priority email support</span>
                  </li>
                </ul>
                <div className="mt-6">
                  <Link href="/dashboard/topup">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="flex flex-col border rounded-lg p-6 shadow-sm">
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold">Enterprise</h3>
                  <p className="text-muted-foreground">
                    For large businesses with high transaction volumes.
                  </p>
                </div>
                <div className="mt-4 flex flex-wrap items-baseline text-3xl font-bold">
                  KSh 50,000
                  <span className="ml-1 text-base font-medium text-muted-foreground">
                    / month
                  </span>
                </div>
                <ul className="mt-6 space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Business compliance tracking</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Tax & statutory reminders</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Automated alerts (Email + SMS + API Webhooks) </span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Access via web & mobile</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Monthly activity reports</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Centralized unlimited storage</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Internal policy compliance tools</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>API & system integrations</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Custom compliance modules</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Real-time dashboards & analytics</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Advanced audit trail & reporting</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Dedicated account manager</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>Onboarding & compliance training</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                    <span>24/7 premium support</span>
                  </li>
                </ul>
                <div className="mt-6">
                  <Link href="/dashboard/topup">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section
          id="testimonials"
          className="w-full py-12 md:py-24 lg:py-32 bg-gray-50 dark:bg-gray-900"
        >
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Trusted by Businesses Across Kenya
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  See what our customers have to say about our Risk & Compliance
                  Management System.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex flex-col border rounded-lg p-6 shadow-sm">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="font-bold text-green-700">JM</span>
                  </div>
                  <div>
                    <h3 className="font-bold">Grace M.</h3>
                    <p className="text-sm text-muted-foreground">
                      Independent Consultant
                    </p>
                  </div>
                </div>
                <p className="mt-4 text-muted-foreground">
                  "Before this platform, staying compliant was a constant
                  headache. Now, I get alerts, track everything in one place,
                  and actually feel in control. It’s a game-changer for
                  freelancers like me!"
                </p>
              </div>
              <div className="flex flex-col border rounded-lg p-6 shadow-sm">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="font-bold text-green-700">SW</span>
                  </div>
                  <div>
                    <h3 className="font-bold">Sarah Wanjiku</h3>
                    <p className="text-sm text-muted-foreground">
                      Mombasa Traders
                    </p>
                  </div>
                </div>
                <p className="mt-4 text-muted-foreground">
                  "Our company used to struggle with missed deadlines and
                  scattered records. This system streamlined everything. It’s
                  like having a full-time compliance officer in your pocket."
                </p>
              </div>
              <div className="flex flex-col border rounded-lg p-6 shadow-sm">
                <div className="flex items-center space-x-2">
                  <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="font-bold text-green-700">DO</span>
                  </div>
                  <div>
                    <h3 className="font-bold">Esther W.</h3>
                    <p className="text-sm text-muted-foreground">
                      Legal & Compliance Officer, Meditech Group
                    </p>
                  </div>
                </div>
                <p className="mt-4 text-muted-foreground">
                  "The real-time tracking and centralized recordkeeping are
                  exactly what we needed. It's user-friendly and has saved us
                  from penalties more than once."
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <LandingFaqSection />

        {/* CTA Section */}
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Ready to Simplify Your Risk and Compliance Management?
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  Join hundreds of businesses across Kenya who trust our
                  platform for their risk and compliance management needs.
                </p>
              </div>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Link href="/dashboard">
                  <Button size="lg" className="bg-green-600 hover:bg-green-700">
                    Get Started Today
                  </Button>
                </Link>
                <Link href="#contact">
                  <Button size="lg" variant="outline">
                    Contact Sales
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section
          id="contact"
          className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-white to-green-50 dark:from-background dark:to-green-950/20"
        >
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
                  Get in Touch
                </h2>
                <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
                  Have questions about our Compliance And Risk Management
                  System? Our team is here to help you.
                </p>
              </div>
            </div>

            <div className="grid gap-10 lg:grid-cols-2 max-w-6xl mx-auto">
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold">Contact Information</h3>
                  <p className="text-muted-foreground">
                    Reach out to us directly or fill out the form, and we'll get
                    back to you as soon as possible.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <Mail className="h-6 w-6 text-green-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold">Email</h4>
                      <p className="text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <Phone className="h-6 w-6 text-green-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold">Phone</h4>
                      <p className="text-muted-foreground">+254 712 345 678</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <MapPin className="h-6 w-6 text-green-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold">Office</h4>
                      <p className="text-muted-foreground">
                        Westlands Business Park
                        <br />
                        Nairobi, Kenya
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
                  <h4 className="font-semibold mb-2">Business Hours</h4>
                  <ul className="space-y-2 text-muted-foreground">
                    <li className="flex justify-between">
                      <span>Monday - Friday:</span>
                      <span>8:00 AM - 6:00 PM</span>
                    </li>
                    <li className="flex justify-between">
                      <span>Saturday:</span>
                      <span>9:00 AM - 1:00 PM</span>
                    </li>
                    <li className="flex justify-between">
                      <span>Sunday:</span>
                      <span>Closed</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
                <h3 className="text-2xl font-bold mb-6">Send Us a Message</h3>
                <ContactForm />
              </div>
            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  );
}
