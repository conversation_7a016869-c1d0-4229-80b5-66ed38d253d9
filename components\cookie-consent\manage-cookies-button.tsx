"use client"

import type React from "react"

import { Button, type ButtonProps } from "@/components/ui/button"
import { useCookieConsent } from "./cookie-context"

interface ManageCookiesButtonProps extends ButtonProps {
  children?: React.ReactNode
}

export function ManageCookiesButton({ children, ...props }: ManageCookiesButtonProps) {
  const { openSettings } = useCookieConsent()

  return (
    <Button onClick={openSettings} {...props}>
      {children || "Manage Cookie Preferences"}
    </Button>
  )
}
