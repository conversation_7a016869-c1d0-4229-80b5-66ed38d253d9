# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Set Node options for memory management
ENV NODE_OPTIONS="--max-old-space-size=2048 --max-semi-space-size=64 --expose-gc"
ENV NEXT_TELEMETRY_DISABLED=1

# Copy package files
COPY package*.json ./

# Install dependencies with reduced memory usage
RUN npm install --no-fund --no-audit --progress=false --prefer-offline

# Copy project files
COPY . .

# Set memory limit for node
ENV NODE_OPTIONS=--max_old_space_size=2048

# Build the static site
RUN npm run build

# Serve stage
FROM nginx:alpine

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

