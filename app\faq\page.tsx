import { LandingHeader } from "@/components/landing-header";
import { LandingFooter } from "@/components/landing-footer";
import { FaqSection } from "@/components/faq-section";

export const metadata = {
  title: "Frequently Asked Questions | Compliance And Risk Management System",
  description:
    "Find answers to common questions about our Phone hash decoding service, pricing, API integration, and more.",
};

export default function FaqPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <LandingHeader />
      <main className="flex-1 container py-12 px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-muted-foreground mb-8">
            Find answers to common questions about our Phone hash decoding
            service. If you can't find what you're looking for, please{" "}
            <a href="/contact" className="text-green-600 hover:underline">
              contact our support team
            </a>
            .
          </p>

          <FaqSection />
        </div>
      </main>
      <LandingFooter />
    </div>
  );
}
