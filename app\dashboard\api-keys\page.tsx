"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Copy,
  Key,
  Plus,
  RefreshCw,
  Trash,
  Loader2,
  Check,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { apiKeysService } from "@/services/api";
import { Api<PERSON><PERSON> } from "@/types";

// Mock data for API keys
const apiKeys = [
  {
    id: "key_1",
    name: "Production API Key",
    key: "mpd_prod_1234567890abcdef1234567890abcdef",
    created: "2023-05-01",
    lastUsed: "2023-05-20",
    status: "active",
  },
  {
    id: "key_2",
    name: "Development API Key",
    key: "mpd_dev_0987654321fedcba0987654321fedcba",
    created: "2023-05-10",
    lastUsed: "2023-05-19",
    status: "active",
  },
  {
    id: "key_3",
    name: "Testing API Key",
    key: "mpd_test_abcdef1234567890abcdef1234567890",
    created: "2023-04-15",
    lastUsed: "2023-05-15",
    status: "inactive",
  },
];

type apiStatus = {
  listing: "PENDING" | "LOADING" | "DONE";
  creating: "PENDING" | "LOADING" | "DONE";
};

export default function ApiKeysPage() {
  const { token, user } = useAuth();
  const [userApiKeys, setUserApiKeys] = useState<ApiKey[] | []>([]);
  const [apiKeyNamae, setApiKeyNamae] = useState("");
  const [apiStatus, setApiStatus] = useState<apiStatus>({
    listing: "PENDING",
    creating: "PENDING",
  });
  const [copiedField, setCopiedField] = useState<number | null>(null);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  useEffect(() => {
    if (token) {
      setApiStatus((prev) => ({ ...prev, listing: "LOADING" }));
      apiKeysService
        .getApiKeys(token)
        .then((res) => {
          setUserApiKeys(res.api_keys);
        })
        .catch((err) => {
          toast.error("Error!", {
            description: "Failed to fetch your api keys.",
          });
        })
        .finally(() => {
          setApiStatus((prev) => ({ ...prev, listing: "DONE" }));
        });
    }
  }, [token]);

  const handleKeyCreation = ({
    key_name = "",
    store_id,
    user_token,
  }: {
    key_name: string;
    store_id?: number;
    user_token: string | null;
  }) => {
    if (!key_name || !store_id || !user_token) return;
    setApiStatus((prev) => ({ ...prev, creating: "LOADING" }));
    setResult(null);
    apiKeysService
      .createApiKey({ description: key_name, store_id }, token)
      .then((data) => {
        setUserApiKeys((prevState) => [data, ...prevState]);
        setApiKeyNamae("");
        setResult(() => ({
          success: true,
          message: "Key generated successfully. Copy from table below",
        }));
      })
      .catch((error) => {
        setResult(() => ({
          success: false,
          message:
            error?.response?.data?.message || "Failed to generate API Key.",
        }));
      })
      .finally(() => setApiStatus((prev) => ({ ...prev, creating: "DONE" })));
  };

  const copyToClipboard = (text: string, field: number) => {
    navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  return (
    <div className="flex flex-col gap-6 overflow-hidden">
      <p className="text-muted-foreground">
        Manage your API keys for integrating with our Phone hash decoding
        service.
      </p>

      <Card>
        <CardHeader>
          <CardTitle>Create New API Key</CardTitle>
          <CardDescription>
            Generate a new API key to access our Phone hash decoding service
            programmatically.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="space-y-2 flex-1">
              <Label htmlFor="key-name">API Key Name</Label>
              <Input
                id="key-name"
                placeholder="Enter a name for your API key"
                value={apiKeyNamae}
                onChange={(e) => setApiKeyNamae(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Give your API key a descriptive name to help you identify it
                later.
              </p>
            </div>
            <Button
              className="bg-green-600 hover:bg-green-700 whitespace-nowrap mt-8"
              disabled={apiStatus.creating === "LOADING" || !apiKeyNamae.trim()}
              onClick={() =>
                handleKeyCreation({
                  key_name: apiKeyNamae,
                  store_id: user?.default_store.id,
                  user_token: token,
                })
              }
            >
              {apiStatus.creating === "LOADING" ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Generate API Key
                </>
              )}
            </Button>
          </div>
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="active">
        <TabsList>
          <TabsTrigger value="active">Active Keys</TabsTrigger>
          <TabsTrigger value="inactive">Inactive Keys</TabsTrigger>
        </TabsList>
        <TabsContent value="active" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>API Key</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {userApiKeys.map((key) => (
                  <TableRow key={key.id}>
                    <TableCell className="font-medium">
                      {key.description}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {key.api_key.substring(0, 10)}...
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-2"
                        onClick={() => copyToClipboard(key.api_key, key.id)}
                      >
                        {copiedField === key.id ? (
                          <Check className="h-3 w-3 text-green-500" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                        <span className="sr-only">Copy API key</span>
                      </Button>
                    </TableCell>
                    <TableCell>
                      {format(key.created_at, "dd MMM yyyy")}
                    </TableCell>
                    <TableCell>
                      {key.last_used_at
                        ? format(key.last_used_at, "dd MMM yyyy")
                        : "Never"}
                    </TableCell>
                    <TableCell>
                      <Badge variant="success">Active</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="icon">
                          <RefreshCw className="h-4 w-4" />
                          <span className="sr-only">Regenerate API key</span>
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete API key</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="inactive" className="space-y-4">
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>API Key</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {apiKeys
                  .filter((key) => key.status === "inactive")
                  .map((key) => (
                    <TableRow key={key.id}>
                      <TableCell className="font-medium">{key.name}</TableCell>
                      <TableCell className="font-mono text-xs">
                        {key.key.substring(0, 10)}...
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-2"
                        >
                          <Copy className="h-3 w-3" />
                          <span className="sr-only">Copy API key</span>
                        </Button>
                      </TableCell>
                      <TableCell>{key.created}</TableCell>
                      <TableCell>{key.lastUsed}</TableCell>
                      <TableCell>
                        <Badge variant="outline">Inactive</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon">
                            <RefreshCw className="h-4 w-4" />
                            <span className="sr-only">Reactivate API key</span>
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash className="h-4 w-4" />
                            <span className="sr-only">Delete API key</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>API Documentation</CardTitle>
          <CardDescription>
            Learn how to integrate our Phone hash decoding service into your
            applications.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="rounded-md bg-muted p-4">
              <h3 className="font-semibold mb-2">Endpoint</h3>
              <p className="font-mono text-sm">
                https://complianceapi.growthlogic.co.ke/api
              </p>
            </div>
            <div className="rounded-md bg-muted p-4">
              <h3 className="font-semibold mb-2">Authentication</h3>
              <p className="text-sm">
                Include your API key in the request headers:
              </p>
              <p className="font-mono text-sm mt-2">
                x-hash-api-key: YOUR_API_KEY
              </p>
            </div>
            <div className="rounded-md bg-muted p-4">
              <h3 className="font-semibold mb-2">
                # GET request to decode a hash
              </h3>
              <p className="font-mono text-sm break-all">
                curl -X GET
                "https://complianceapi.growthlogic.co.ke/api/hashes/hash/200020001234567890123456789012345678901234"{" "}
                <br />
                -H "x-hash-api-key: YOUR_API_KEY"
              </p>
            </div>
            <div className="rounded-md bg-muted p-4">
              <h3 className="font-semibold mb-2" title="Example Requests">
                # GET request to generate a hash
              </h3>
              <p className="font-mono text-sm break-all">
                curl -X GET
                "https://complianceapi.growthlogic.co.ke/api/hashes/phone_number/254712345678"{" "}
                <br />
                -H "x-hash-api-key: YOUR_API_KEY"
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Button variant="outline" className="flex items-center">
              <Key className="mr-2 h-4 w-4" />
              View Full API Documentation
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
