// Types
export interface PhoneNumberHash {
  id: string;
  hash: string;
  phone_number: string;
  new_balance?: number;
  provider: string;
  created_at?: string;
  updated_at?: string;
}

export interface Store {
  id: number;
  avatar_url: string;
  country_code: string;
  email: string;
  etims_branch: string;
  etims_cmc_key: string;
  etims_device_id: string;
  etims_device_serial: string;
  etims_environment: string;
  etims_intrl_key: string;
  etims_mrc_no: string;
  etims_sdcid: string;
  etims_sign_key: string;
  is_licensed: boolean;
  location: string;
  name: string;
  organization_id: number;
  phone_number: string;
  pin: string;
  vat: string;
  website: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: number;
  name: string;
  address: string;
  branch: string;
  contact_person: string;
  date_format: string;
  email: string;
  phone_number_id: number;
  phone_number: string | null;
  is_synced: boolean;
  created_at: string;
  updated_at: string;
}

export interface WholeUser {
  id: number;
  default_store: Store;
  email: string;
  first_name: string;
  last_name: string;
  organization_id: number;
  organization: Organization;
  phone_number_id: number;
  phone_number: string | null;
  username: string;
  is_synced: boolean;
  national_id: string;
  status: string;
  stores: Store[];
  token: string;
  created_at: string;
  updated_at: string;
}

export interface ApiKey {
  id: number;
  api_key: string;
  description: string;
  last_used_at: string | null;
  organization_id: number;
  store_id: number;
  store?: Store | null;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface JwtPayload {
  Email: string;
  ExpiresAt: number;
  Issuer: string;
  application_type: string;
  org_id: number;
  session_id: number;
  user_id: number;
}

export interface DashboardAnalytics {
  account_number: string;
  api_keys_count: number;
  user_credits: number;
  chart_summary: any[];
}
