"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

type CookiePreferences = {
  necessary: boolean
  functional: boolean
  analytics: boolean
  marketing: boolean
}

type CookieConsentContextType = {
  preferences: CookiePreferences
  hasConsented: boolean
  showBanner: boolean
  acceptAll: () => void
  declineAll: () => void
  savePreferences: (prefs: CookiePreferences) => void
  openSettings: () => void
  closeSettings: () => void
  showSettings: boolean
}

const defaultPreferences: CookiePreferences = {
  necessary: true, // Always required
  functional: false,
  analytics: false,
  marketing: false,
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined)

export function CookieConsentProvider({ children }: { children: React.ReactNode }) {
  const [preferences, setPreferences] = useState<CookiePreferences>(defaultPreferences)
  const [hasConsented, setHasConsented] = useState<boolean>(false)
  const [showBanner, setShowBanner] = useState<boolean>(false)
  const [showSettings, setShowSettings] = useState<boolean>(false)

  // Load saved preferences on mount
  useEffect(() => {
    const savedConsent = localStorage.getItem("cookieConsent")
    const savedPreferences = localStorage.getItem("cookiePreferences")

    if (savedConsent === "true") {
      setHasConsented(true)
      setShowBanner(false)
      if (savedPreferences) {
        try {
          setPreferences(JSON.parse(savedPreferences))
        } catch (e) {
          console.error("Error parsing saved cookie preferences", e)
        }
      }
    } else {
      // Show banner if no consent has been given
      setShowBanner(true)
    }
  }, [])

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true,
    }
    setPreferences(allAccepted)
    setHasConsented(true)
    setShowBanner(false)
    localStorage.setItem("cookieConsent", "true")
    localStorage.setItem("cookiePreferences", JSON.stringify(allAccepted))
  }

  const declineAll = () => {
    const allDeclined = {
      necessary: true, // Necessary cookies are always required
      functional: false,
      analytics: false,
      marketing: false,
    }
    setPreferences(allDeclined)
    setHasConsented(true)
    setShowBanner(false)
    localStorage.setItem("cookieConsent", "true")
    localStorage.setItem("cookiePreferences", JSON.stringify(allDeclined))
  }

  const savePreferences = (prefs: CookiePreferences) => {
    // Ensure necessary cookies are always enabled
    const updatedPrefs = { ...prefs, necessary: true }
    setPreferences(updatedPrefs)
    setHasConsented(true)
    setShowBanner(false)
    setShowSettings(false)
    localStorage.setItem("cookieConsent", "true")
    localStorage.setItem("cookiePreferences", JSON.stringify(updatedPrefs))
  }

  const openSettings = () => {
    setShowSettings(true)
  }

  const closeSettings = () => {
    setShowSettings(false)
  }

  return (
    <CookieConsentContext.Provider
      value={{
        preferences,
        hasConsented,
        showBanner,
        showSettings,
        acceptAll,
        declineAll,
        savePreferences,
        openSettings,
        closeSettings,
      }}
    >
      {children}
    </CookieConsentContext.Provider>
  )
}

export function useCookieConsent() {
  const context = useContext(CookieConsentContext)
  if (context === undefined) {
    throw new Error("useCookieConsent must be used within a CookieConsentProvider")
  }
  return context
}
