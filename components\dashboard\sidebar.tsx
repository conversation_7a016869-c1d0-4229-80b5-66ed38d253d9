"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  CreditCard,
  FileText,
  Key,
  LayoutDashboard,
  Settings,
} from "lucide-react";

// todo: disable routes to be used by admins only
export const sidebarItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  // { title: "Reports", href: "/dashboard/reports", icon: BarChart3, },
  {
    title: "Balance",
    href: "/dashboard/balance",
    icon: CreditCard,
  },
  {
    title: "Top-up Credits",
    href: "/dashboard/topup",
    icon: CreditCard,
  },
  // { title: "Requests", href: "/dashboard/requests", icon: FileText, },
  {
    title: "API Keys",
    href: "/dashboard/api-keys",
    icon: Key,
  },
  {
    title: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
  },
];

export function DashboardSidebar({
  isOpen,
  onNavigate,
}: {
  isOpen: boolean;
  onNavigate?: () => void;
}) {
  const pathname = usePathname();

  return (
    <div
      className={cn(
        "h-screen fixed top-0 left-0 bg-background border-r z-30 transition-all duration-300 ease-in-out",
        isOpen ? "w-[260px]" : "w-0 -ml-[1px] overflow-hidden"
      )}
    >
      <div className="flex h-16 items-center border-b px-4 lg:px-6">
        <Link href="/dashboard" className="flex items-center space-x-2">
          <div className="h-6 w-6 rounded-full bg-green-600 flex items-center justify-center">
            <span className="font-bold text-white text-xs">M</span>
          </div>
          <span className="font-bold">Phone Hash Decoder</span>
        </Link>
      </div>
      <div className="h-[calc(100vh-4rem)] overflow-y-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              onClick={onNavigate}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary",
                pathname === item.href
                  ? "bg-muted text-primary"
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.title}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  );
}
