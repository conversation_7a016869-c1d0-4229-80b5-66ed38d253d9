"use client";

import type React from "react";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Search } from "lucide-react";

// Define the FAQ data structure
type FaqItem = {
  question: string;
  answer: React.ReactNode;
  category: string;
  keywords: string[];
};

// FAQ data organized by categories
const faqData: FaqItem[] = [
  // General Questions
  {
    question: "What is Compliance And Risk Management System?",
    answer: (
      <p>
        The Risk Management and Compliance System is a secure, cloud-based
        platform—accessible via web and mobile—that helps individuals and
        organizations in Kenya manage their legal, tax, and internal compliance
        obligations. It provides real-time tracking, automated alerts,
        centralized recordkeeping, and insightful reporting to ensure you stay
        ahead of regulatory requirements, avoid penalties, and maintain
        operational integrity. Whether you're a freelancer, consultant, SME, or
        corporate entity, the system simplifies complex compliance tasks and
        supports good governance practices—all in one easy-to-use dashboard.
      </p>
    ),
    category: "general",
    keywords: ["about", "service", "platform", "what is", "introduction"],
  },
  {
    question: "How does Compliance And Risk Management System work?",
    answer: (
      <div className="space-y-2">
        <p>Our platform works in four simple steps:</p>
        <ol className="list-decimal pl-5 space-y-1">
          <li>
            Sign Up - Create your account and customize your compliance
            profile—whether you’re an individual or a business.
          </li>
          <li>
            Add Obligations - Input or sync your tax, legal, and policy
            requirements. Set due dates, rules, and reminders.
          </li>
          <li>
            Track & Get Alerts - Monitor your compliance status in real time and
            receive automated alerts before deadlines or risks arise.
          </li>
          <li>
            Access Reports & Records - Download reports, view your history, and
            keep all documents safely stored in one centralized place.
          </li>
        </ol>
        <p>
          We provide a secure, all-in-one platform that empowers individuals and
          organizations to effortlessly manage their legal, tax, and internal
          compliance obligations.
        </p>
      </div>
    ),
    category: "general",
    keywords: ["process", "how it works", "steps", "decode", "function"],
  },
  {
    question: "Which countries are you operating in?",
    answer: (
      <div className="space-y-2">
        <p>We are currently offering services in the following countries:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Kenya</li>
          <li>Tanzania</li>
          <li>Democratic Republic of Congo</li>
          <li>Mozambique</li>
          <li>Lesotho</li>
          <li>Ghana</li>
          <li>Egypt</li>
        </ul>
        <p>
          We're continuously expanding our coverage to include more regions.
        </p>
      </div>
    ),
    category: "general",
    keywords: [
      "countries",
      "regions",
      "support",
      "coverage",
      "locations",
      "safaricom",
      "vodacom",
    ],
  },

  // Account & Billing Questions
  {
    question: "How much does the service cost?",
    answer: (
      <div className="space-y-2">
        <p>
          Our service operates on a credit-based system with the following
          packages:
        </p>
        <ul className="list-disc pl-5 space-y-1">
          <li>
            <strong>Starter:</strong> KSh 1,000 per month. Perfect for small
            businesses just getting started.
          </li>
          <li>
            <strong>Business:</strong> KSH 5,000 per month. Ideal for growing
            businesses with moderate volume.
          </li>
          <li>
            <strong>Enterprise:</strong> KSH 50,000 per month. For large
            businesses with high transaction volumes.
          </li>
        </ul>
        <p>
          We also offer custom packages for high-volume users. Please contact
          our sales team for more information.
        </p>
      </div>
    ),
    category: "billing",
    keywords: ["price", "cost", "pricing", "packages", "credits", "payment"],
  },
  {
    question: "What payment methods do you accept?",
    answer: (
      <p>
        We accept payments via M-Pesa and major credit/debit cards. For business
        and enterprise customers, we also offer invoice-based payments with
        terms. All transactions are processed securely through our payment
        partners.
      </p>
    ),
    category: "billing",
    keywords: ["payment", "methods", "credit card", "mpesa", "invoice", "pay"],
  },
  {
    question: "Do credits expire?",
    answer: (
      <p>
        No, your purchased credits do not expire. Once you buy credits, they
        remain in your account until used. This gives you flexibility to use our
        service according to your business needs without worrying about time
        limitations.
      </p>
    ),
    category: "billing",
    keywords: ["expiry", "expire", "validity", "credits", "time limit"],
  },
  {
    question: "Can I get a refund for unused credits?",
    answer: (
      <p>
        As per our Terms of Service, credits purchased are non-refundable.
        However, in exceptional circumstances, we may consider refund requests
        on a case-by-case basis. Please contact our support team if you have a
        specific situation that requires consideration.
      </p>
    ),
    category: "billing",
    keywords: ["refund", "money back", "return", "cancel", "unused"],
  },

  // Technical Questions
  // {
  //   question: "What information can be extracted from an Phone hash?",
  //   answer: (
  //     <div className="space-y-2">
  //       <p>
  //         Our decoder can extract the following information from a valid M-Pesa
  //         transaction hash:
  //       </p>
  //       <ul className="list-disc pl-5 space-y-1">
  //         <li>Transaction ID</li>
  //         <li>Transaction date and time</li>
  //         <li>Sender's phone number</li>
  //         <li>Recipient's information</li>
  //         <li>Transaction amount</li>
  //         <li>Transaction type</li>
  //         <li>Transaction status</li>
  //       </ul>
  //       <p>
  //         The exact information available may vary depending on the type of
  //         transaction and the M-Pesa service provider.
  //       </p>
  //     </div>
  //   ),
  //   category: "technical",
  //   keywords: ["extract", "information", "data", "details", "transaction"],
  // },
  // {
  //   question: "How do I find the Mobile-money transaction hash?",
  //   answer: (
  //     <div className="space-y-2">
  //       <p>
  //         The Mobile-money transaction hash is typically provided in the
  //         callback response when an M-Pesa payment is made to your business. You
  //         can find it in:
  //       </p>
  //       <ul className="list-disc pl-5 space-y-1">
  //         <li>The callback payload sent to your integration endpoint</li>
  //         <li>
  //           The response from the M-Pesa API when querying transaction status
  //         </li>
  //         <li>Your M-Pesa business portal transaction records</li>
  //       </ul>
  //       <p>
  //         If you're having trouble locating the transaction hash, please refer
  //         to the M-Pesa integration documentation for your specific country or
  //         contact our support team for assistance.
  //       </p>
  //     </div>
  //   ),
  //   category: "technical",
  //   keywords: ["find", "locate", "get", "hash", "where"],
  // },
  // {
  //   question: "What is the format of an Mobile-money transaction hash?",
  //   answer: (
  //     <p>
  //       An Mobile-money transaction hash is typically a long alphanumeric
  //       string. The exact format may vary depending on the M-Pesa provider and
  //       country, but it's generally between 30-50 characters long. Our system
  //       can recognize and process valid Mobile-money transaction hashes
  //       regardless of their specific format.
  //     </p>
  //   ),
  //   category: "technical",
  //   keywords: ["format", "structure", "pattern", "hash", "string"],
  // },

  // API & Integration Questions
  {
    question: "How do I integrate with your API?",
    answer: (
      <div className="space-y-2">
        <p>Integrating with our API is straightforward:</p>
        <ol className="list-decimal pl-5 space-y-1">
          <li>Sign up for an account and purchase credits</li>
          <li>Generate an API key from your dashboard</li>
          <li>Use our RESTful API to submit transaction hashes for decoding</li>
          <li>Process the returned JSON response in your application</li>
        </ol>
        <p>
          We provide comprehensive API documentation, code examples in multiple
          programming languages, and SDKs to make integration as simple as
          possible. Visit the API documentation section in your dashboard after
          signing up.
        </p>
      </div>
    ),
    category: "api",
    keywords: ["integrate", "api", "implementation", "code", "developer"],
  },
  {
    question: "Is there a rate limit for API requests?",
    answer: (
      <p>
        Yes, we implement rate limiting to ensure service stability. Standard
        accounts are limited to 10 requests per second. Business and Enterprise
        accounts have higher limits of 50 and 100 requests per second
        respectively. If you need higher limits, please contact our support team
        to discuss your requirements.
      </p>
    ),
    category: "api",
    keywords: ["rate limit", "requests", "throttling", "api", "limits"],
  },
  {
    question: "Do you provide webhooks for asynchronous processing?",
    answer: (
      <p>
        Yes, we support webhook notifications for asynchronous processing. This
        is particularly useful for bulk uploads or when processing large volumes
        of transaction hashes. You can configure webhook endpoints in your
        dashboard, and our system will send notifications when processing is
        complete or if any issues arise.
      </p>
    ),
    category: "api",
    keywords: ["webhook", "callback", "notification", "async", "asynchronous"],
  },

  // Security & Compliance Questions
  {
    question: "How secure is your service?",
    answer: (
      <div className="space-y-2">
        <p>
          Security is our top priority. We implement multiple layers of
          protection:
        </p>
        <ul className="list-disc pl-5 space-y-1">
          <li>All data is encrypted in transit using TLS/SSL</li>
          <li>Sensitive data is encrypted at rest using AES-256</li>
          <li>
            API keys are securely hashed and never displayed after generation
          </li>
          <li>Regular security audits and penetration testing</li>
          <li>Strict access controls and authentication mechanisms</li>
          <li>Compliance with relevant data protection regulations</li>
        </ul>
        <p>
          We also maintain comprehensive security logs and monitoring systems to
          detect and respond to any unusual activities.
        </p>
      </div>
    ),
    category: "security",
    keywords: ["security", "protection", "encryption", "safe", "secure"],
  },
  {
    question: "Is your service compliant with data protection regulations?",
    answer: (
      <p>
        Yes, our service is designed to comply with relevant data protection
        regulations, including the Kenya Data Protection Act of 2019 and similar
        regulations in other African countries where we operate. We implement
        appropriate technical and organizational measures to protect personal
        data, respect data subject rights, and maintain records of our data
        processing activities. For more information, please refer to our Privacy
        Policy.
      </p>
    ),
    category: "security",
    keywords: ["compliance", "regulations", "gdpr", "data protection", "legal"],
  },
  {
    question: "How long do you retain transaction data?",
    answer: (
      <p>
        We retain transaction data for a period of 7 years to comply with
        financial regulations in the countries where we operate. This data is
        securely stored and accessible only to authorized personnel. You can
        access your historical transaction data through your dashboard at any
        time during this retention period. After the retention period, data is
        securely deleted or anonymized in accordance with our data retention
        policies.
      </p>
    ),
    category: "security",
    keywords: ["retention", "storage", "data", "delete", "keep"],
  },

  // Support & Troubleshooting
  {
    question: "What should I do if a hash cannot be decoded?",
    answer: (
      <div className="space-y-2">
        <p>If a hash cannot be decoded, please check the following:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Verify that the hash is correctly formatted and complete</li>
          <li>Ensure the transaction is from a supported M-Pesa service</li>
          <li>
            Check if the transaction is recent (some very old transactions may
            not be decodable)
          </li>
        </ul>
        <p>
          If you've verified these points and still encounter issues, please
          contact our support team with the hash details, and we'll investigate
          further. In cases where our service fails to decode a valid M-Pesa
          transaction hash, the credit used may be refunded to your account at
          our discretion.
        </p>
      </div>
    ),
    category: "support",
    keywords: ["error", "failed", "cannot decode", "problem", "issue"],
  },
  {
    question: "How can I contact customer support?",
    answer: (
      <div className="space-y-2">
        <p>
          You can reach our customer support team through multiple channels:
        </p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Email: <EMAIL></li>
          <li>
            Phone: +254 712 345 678 (Monday to Friday, 8:00 AM - 6:00 PM EAT)
          </li>
          <li>Live chat on our website (available during business hours)</li>
          <li>Contact form on our website</li>
        </ul>
        <p>
          For urgent matters outside business hours, please use the emergency
          contact form in your dashboard, and our on-call support team will
          respond as soon as possible.
        </p>
      </div>
    ),
    category: "support",
    keywords: ["contact", "support", "help", "assistance", "reach"],
  },
  {
    question: "Do you offer training or onboarding assistance?",
    answer: (
      <p>
        Yes, we provide onboarding assistance for all new customers. Business
        and Enterprise customers also receive complimentary training sessions to
        help them get the most out of our platform. Additionally, we offer
        comprehensive documentation, video tutorials, and regular webinars on
        various aspects of our service. If you need personalized training,
        please contact our customer success team to arrange a session.
      </p>
    ),
    category: "support",
    keywords: ["training", "onboarding", "learn", "tutorial", "help"],
  },
];

export function FaqSection() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Filter FAQs based on search query and active tab
  const filteredFaqs = faqData.filter((faq) => {
    const matchesSearch =
      searchQuery === "" ||
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.keywords.some((keyword) =>
        keyword.toLowerCase().includes(searchQuery.toLowerCase())
      );

    const matchesCategory = activeTab === "all" || faq.category === activeTab;

    return matchesSearch && matchesCategory;
  });

  // Group FAQs by category for the "all" tab
  const groupedFaqs = filteredFaqs.reduce((acc, faq) => {
    if (!acc[faq.category]) {
      acc[faq.category] = [];
    }
    acc[faq.category].push(faq);
    return acc;
  }, {} as Record<string, FaqItem[]>);

  // Category labels for display
  const categoryLabels: Record<string, string> = {
    general: "General Questions",
    billing: "Account & Billing",
    technical: "Technical Questions",
    api: "API & Integration",
    security: "Security & Compliance",
    support: "Support & Troubleshooting",
  };

  return (
    <div className="space-y-6">
      {/* Search bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search for answers..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Category tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="w-full h-auto flex flex-wrap justify-start gap-2 bg-transparent">
          <TabsTrigger
            value="all"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            All Categories
          </TabsTrigger>
          <TabsTrigger
            value="general"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            General
          </TabsTrigger>
          <TabsTrigger
            value="billing"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            Account & Billing
          </TabsTrigger>
          <TabsTrigger
            value="technical"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            Technical
          </TabsTrigger>
          <TabsTrigger
            value="api"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            API & Integration
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            Security
          </TabsTrigger>
          <TabsTrigger
            value="support"
            className="data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400"
          >
            Support
          </TabsTrigger>
        </TabsList>

        {/* All categories tab content */}
        <TabsContent value="all" className="mt-6">
          {Object.keys(groupedFaqs).length > 0 ? (
            Object.entries(groupedFaqs).map(([category, faqs]) => (
              <div key={category} className="mb-8">
                <h2 className="text-xl font-semibold mb-4">
                  {categoryLabels[category]}
                </h2>
                <Accordion type="single" collapsible className="space-y-4">
                  {faqs.map((faq, index) => (
                    <AccordionItem
                      key={index}
                      value={`${category}-${index}`}
                      className="border rounded-lg px-4"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-4 text-muted-foreground">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No FAQs found matching your search. Please try different
                keywords.
              </p>
            </div>
          )}
        </TabsContent>

        {/* Individual category tab contents */}
        {["general", "billing", "technical", "api", "security", "support"].map(
          (category) => (
            <TabsContent key={category} value={category} className="mt-6">
              {filteredFaqs.length > 0 ? (
                <Accordion type="single" collapsible className="space-y-4">
                  {filteredFaqs.map((faq, index) => (
                    <AccordionItem
                      key={index}
                      value={`${category}-${index}`}
                      className="border rounded-lg px-4"
                    >
                      <AccordionTrigger className="text-left hover:no-underline py-4">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="pt-2 pb-4 text-muted-foreground">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    No FAQs found matching your search. Please try different
                    keywords.
                  </p>
                </div>
              )}
            </TabsContent>
          )
        )}
      </Tabs>

      {/* Still need help section */}
      <div className="mt-12 p-6 bg-muted rounded-lg text-center">
        <h3 className="text-lg font-semibold mb-2">Still have questions?</h3>
        <p className="mb-4">
          Our support team is ready to help you with any questions you may have.
        </p>
        <a
          href="/contact"
          className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white shadow hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Contact Support
        </a>
      </div>
    </div>
  );
}
